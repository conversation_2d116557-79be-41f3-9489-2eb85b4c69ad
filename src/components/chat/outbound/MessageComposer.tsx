import { useState, useCallback, useRef, useEffect } from "react"
import { useDropzone } from "react-dropzone"
import { generateClient } from "aws-amplify/api"
import { executeSubscription } from "@/lib/graphql-client"
import { ON_MEDIA_UPLOAD_STATUS_CHANGED } from "@/lib/graphql-operations"
import { useAuth } from "@/hooks/useAuth"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { uploadFileToS3 } from "@/lib/s3/upload"
import {
  SEND_MESSAGE,
  SendMessageInput,
  SendMessageResponse
} from "@/lib/graphql/mutations/sendMessage"
import {
  Plus,
  Send,
  Smile,
  Mic,
  Loader2,
  CheckCircle,
  FolderClosed,
  CircleUserRound,
  X,
  FileImage,
  Images,
  Upload,
  ZoomIn,
  CalendarDays,
  File
} from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Pop<PERSON>,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover"
import EmojiPicker, { EmojiClickData, Theme } from "emoji-picker-react"
import { cn } from "@/lib/utils"

// Lazy Image Component with Skeleton Loading
interface LazyImageProps {
  src: string
  alt: string
  className?: string
  onClick?: () => void
  fallbackIcon?: React.ReactNode
}

const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  className,
  onClick,
  fallbackIcon
}) => {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(false)
  const imgRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const handleLoad = () => {
    setIsLoading(false)
    setHasError(false)
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  return (
    <div
      ref={imgRef}
      className={cn("relative overflow-hidden", className)}
      onClick={onClick}
    >
      {/* Skeleton Loading */}
      {isLoading && (
        <div className="absolute inset-0 bg-[#4a5c6a] animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 bg-[#5a6c7a] rounded animate-pulse" />
        </div>
      )}

      {/* Error State */}
      {hasError && (
        <div className="absolute inset-0 bg-[#4a5c6a] flex items-center justify-center">
          {fallbackIcon || <FileImage className="h-6 w-6 text-[#8696a0]" />}
        </div>
      )}

      {/* Actual Image */}
      {isInView && (
        <img
          src={src}
          alt={alt}
          className={cn(
            "w-full h-full object-cover transition-opacity duration-300",
            isLoading ? "opacity-0" : "opacity-100"
          )}
          onLoad={handleLoad}
          onError={handleError}
          loading="lazy"
        />
      )}
    </div>
  )
}

interface MessageComposerProps {
  conversationId: string
  onMessageSent?: (message: any) => void
  disabled?: boolean
  className?: string
}

// Enhanced media upload status tracking
interface MediaUpload {
  id: string // Unique identifier for each file
  s3Key: string
  fileName: string
  mediaId?: string
  status: "UPLOADING" | "PROCESSING" | "READY" | "FAILED"
  fileSize: number
  mediaType: string // Store the actual file MIME type
  progress?: number
  previewUrl?: string
  file?: File // Store original file for preview
}

export const MessageComposer: React.FC<MessageComposerProps> = ({
  conversationId,
  onMessageSent,
  disabled = false,
  className = ""
}) => {
  const { user } = useAuth()
  const { toast } = useToast()
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // State
  const [textContent, setTextContent] = useState("")
  const [sending, setSending] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [mediaUploads, setMediaUploads] = useState<MediaUpload[]>([])
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false)
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null)
  const [isMediaMenuOpen, setIsMediaMenuOpen] = useState(false)
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false)
  const [isDragActive, setIsDragActive] = useState(false)

  // Initialize Amplify GraphQL client
  const client = generateClient()

  // Subscribe to media upload status updates
  useEffect(() => {
    if (
      mediaUploads.length === 0 ||
      mediaUploads.every((upload) => upload.status === "UPLOADING")
    )
      return

    console.log(
      "🎬 Setting up media upload subscription for conversation:",
      conversationId
    )

    const subscription = executeSubscription(
      {
        query: ON_MEDIA_UPLOAD_STATUS_CHANGED,
        variables: { conversationId }
      },
      {
        next: (data: any) => {
          console.log("🎬 Media upload status update:", data)
          const update = data.onMediaUploadStatusChanged

          // Find matching upload by filename
          const backendFilename = update?.s3Key.split("/").pop()
          const matchingUpload = mediaUploads.find((upload) => {
            const frontendFilename = upload.s3Key.split("/").pop()
            return frontendFilename === backendFilename
          })

          if (update && matchingUpload) {
            console.log(
              `🔍 Updating upload ${matchingUpload.id} status to: ${update.status}`
            )

            setMediaUploads((prev) =>
              prev.map((upload) =>
                upload.id === matchingUpload.id
                  ? {
                      ...upload,
                      status: update.status, // Keep backend status format (uppercase)
                      mediaId: update.mediaId || upload.mediaId,
                      fileName: update.fileName || upload.fileName,
                      fileSize:
                        update.fileSize !== undefined
                          ? update.fileSize
                          : upload.fileSize
                    }
                  : upload
              )
            )

            // Show success toast when ready
            if (update.status === "READY") {
              console.log("🎉 File is READY! Showing toast...")
              const isImage = matchingUpload.mediaType.startsWith("image/")
              toast({
                title: `✅ ${isImage ? "Image" : "File"} Ready`,
                description: `${matchingUpload.fileName} is ready to send!`
              })
            }
          }
        },
        error: (error: any) => {
          console.error("Media upload subscription error:", error)
          toast({
            title: "Connection Error",
            description: "Lost connection to upload updates",
            variant: "destructive"
          })
        }
      }
    )

    return () => {
      subscription?.unsubscribe()
    }
  }, [mediaUploads, conversationId, toast])

  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = "auto"
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120)
      textarea.style.height = newHeight + "px"
    }
  }, [])

  const uploadSingleFile = useCallback(
    async (file: File) => {
      if (!user?.organizationId) return

      // Extract phoneNumberId from conversationId (format: orgId#phoneNumberId#customerPhone)
      const conversationParts = conversationId.split("#")
      const phoneNumberId =
        conversationParts.length >= 2 ? conversationParts[1] : ""

      if (!phoneNumberId) {
        toast({
          title: "Upload Failed",
          description: "Could not determine phone number ID from conversation.",
          variant: "destructive"
        })
        return
      }

      // Create preview URL for internal use
      const previewUrl = URL.createObjectURL(file)
      const fileId = `${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`

      // Create new media upload object
      const newMediaUpload: MediaUpload = {
        id: fileId,
        s3Key: "", // Will be set after upload
        fileName: file.name,
        mediaType: file.type, // Store actual MIME type (e.g., 'image/png', 'image/jpeg')
        status: "UPLOADING",
        fileSize: file.size,
        progress: 0,
        previewUrl, // Store it but UI will only show when status is READY
        file
      }

      // Add to uploads array
      setMediaUploads((prev) => [...prev, newMediaUpload])

      const isImage = file.type.startsWith("image/")
      toast({
        title: `📤 Uploading ${isImage ? "Image" : "File"}`,
        description: `Preparing ${file.name} for upload...`
      })

      const uploadResult = await uploadFileToS3(
        file,
        user.organizationId,
        conversationId,
        phoneNumberId,
        (progress) => {
          console.log(`Upload progress:`, progress)
          if (
            progress &&
            progress.loaded !== undefined &&
            progress.total !== undefined
          ) {
            const percentage = Math.round(
              (progress.loaded / progress.total) * 100
            )
            setMediaUploads((prev) =>
              prev.map((upload) =>
                upload.id === fileId
                  ? { ...upload, progress: percentage }
                  : upload
              )
            )
          }
        }
      )

      if (uploadResult.success && uploadResult.key) {
        // Update to processing state
        setMediaUploads((prev) =>
          prev.map((upload) =>
            upload.id === fileId
              ? {
                  ...upload,
                  s3Key: uploadResult.key!,
                  status: "PROCESSING",
                  progress: 100
                }
              : upload
          )
        )

        const isImage = file.type.startsWith("image/")
        toast({
          title: `🔄 Processing ${isImage ? "Image" : "File"}`,
          description: `Optimizing ${file.name} for WhatsApp...`
        })
      } else {
        // Clean up preview URL and remove from uploads
        URL.revokeObjectURL(previewUrl)
        setMediaUploads((prev) => prev.filter((upload) => upload.id !== fileId))

        toast({
          title: "Upload Failed",
          description: `Could not upload ${file.name}. Please try again.`,
          variant: "destructive"
        })
      }

      // File input reset is no longer needed since we're using dropzone
    },
    [user?.organizationId, conversationId, toast, setMediaUploads]
  )

  const handleRemoveMedia = useCallback(
    (fileId: string) => {
      const uploadToRemove = mediaUploads.find((upload) => upload.id === fileId)
      if (uploadToRemove?.previewUrl) {
        URL.revokeObjectURL(uploadToRemove.previewUrl)
      }
      setMediaUploads((prev) => prev.filter((upload) => upload.id !== fileId))
      toast({
        title: "Media Removed",
        description: "File removed from message"
      })
    },
    [mediaUploads, toast]
  )

  // Drag and Drop functionality
  const onDrop = useCallback(
    async (acceptedFiles: File[], rejectedFiles: any[]) => {
      setIsDragActive(false)

      if (!user?.organizationId) return

      // Handle rejected files
      if (rejectedFiles.length > 0) {
        rejectedFiles.forEach((rejection) => {
          const file = rejection.file
          let errorMessage = `${file.name}: Upload failed`

          if (rejection.errors.some((e: any) => e.code === "file-too-large")) {
            errorMessage = `${file.name}: File is too large. Maximum size is 16MB`
          } else if (
            rejection.errors.some((e: any) => e.code === "file-invalid-type")
          ) {
            errorMessage = `${file.name}: Invalid file type. Please select an image or document file`
          }

          toast({
            title: "Upload Error",
            description: errorMessage,
            variant: "destructive"
          })
        })
      }

      // Process accepted files
      if (acceptedFiles.length > 0) {
        for (const file of acceptedFiles) {
          await uploadSingleFile(file)
        }
      }
    },
    [user?.organizationId, uploadSingleFile, toast]
  )

  const {
    getRootProps,
    getInputProps,
    isDragActive: dropzoneIsDragActive
  } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"],
      "application/*": [
        ".pdf",
        ".doc",
        ".docx",
        ".xls",
        ".xlsx",
        ".ppt",
        ".pptx"
      ],
      "text/*": [".txt", ".csv"]
    },
    maxFiles: 10,
    maxSize: 16 * 1024 * 1024, // 16MB
    noClick: true, // Disable click to open file dialog (we handle this separately)
    noKeyboard: true
  })

  // Create separate dropzone instances for different file types
  const { open: openImagePicker } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".png", ".jpg", ".jpeg", ".gif", ".webp"]
    },
    maxFiles: 10,
    maxSize: 16 * 1024 * 1024,
    noClick: true,
    noKeyboard: true,
    noDrag: true
  })

  const { open: openDocumentPicker } = useDropzone({
    onDrop,
    accept: {
      "application/*": [
        ".pdf",
        ".doc",
        ".docx",
        ".xls",
        ".xlsx",
        ".ppt",
        ".pptx"
      ],
      "text/*": [".txt", ".csv"]
    },
    maxFiles: 10,
    maxSize: 16 * 1024 * 1024,
    noClick: true,
    noKeyboard: true,
    noDrag: true
  })

  // Use dropzone's isDragActive or our custom state
  const showDragOverlay = isDragActive || dropzoneIsDragActive

  const handleAttachClick = () => {
    setIsMediaMenuOpen(!isMediaMenuOpen)
  }

  const handleMediaMenuItemClick = (
    type: "image" | "camera" | "document" | "location" | "contact"
  ) => {
    console.log(`Opening ${type} picker...`)
    // setIsMediaMenuOpen(false)

    switch (type) {
      case "image":
        // Use dropzone image picker
        openImagePicker()
        break
      case "camera":
        // Camera functionality - for now just open image picker
        openImagePicker()
        break
      case "document":
        // Use dropzone document picker
        openDocumentPicker()
        break
      case "location":
        toast({
          title: "Coming Soon",
          description: "Location sharing will be available soon"
        })
        break
      case "contact":
        toast({
          title: "Coming Soon",
          description: "Contact sharing will be available soon"
        })
        break
    }
  }

  const handleTextChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setTextContent(e.target.value)
      adjustTextareaHeight()
    },
    [adjustTextareaHeight]
  )

  // Emoji picker handlers
  const handleEmojiClick = useCallback(
    (emojiData: EmojiClickData) => {
      const emoji = emojiData.emoji
      const textarea = textareaRef.current
      if (textarea) {
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        const newText =
          textContent.slice(0, start) + emoji + textContent.slice(end)
        setTextContent(newText)

        // Set cursor position after emoji
        setTimeout(() => {
          textarea.focus()
          textarea.setSelectionRange(start + emoji.length, start + emoji.length)
        }, 0)
      } else {
        setTextContent((prev) => prev + emoji)
      }
      setIsEmojiPickerOpen(false)
    },
    [textContent]
  )

  const handleEmojiButtonClick = useCallback(() => {
    setIsEmojiPickerOpen(!isEmojiPickerOpen)
  }, [isEmojiPickerOpen])

  const handleSend = useCallback(async () => {
    const hasText = textContent.trim().length > 0
    const readyMedia = mediaUploads.find(
      (upload) =>
        upload.s3Key &&
        (upload.status === "READY" || upload.status === "PROCESSING")
    )
    const hasMedia = !!readyMedia

    if ((!hasText && !hasMedia) || !user?.organizationId || sending) return

    setSending(true)

    try {
      const input: SendMessageInput = {
        conversationId,
        organizationId: user.organizationId,
        messageType: hasMedia
          ? readyMedia.mediaType.startsWith("image/")
            ? "IMAGE"
            : "DOCUMENT"
          : "TEXT",
        text: hasText ? textContent.trim() : undefined,
        mediaUrl: hasMedia ? readyMedia.s3Key : undefined, // Use S3 key instead of mediaId
        mediaType: hasMedia ? readyMedia.mediaType : undefined, // Use actual file MIME type
        fileName: hasMedia ? readyMedia.fileName : undefined,
        fileSize: hasMedia ? readyMedia.fileSize : undefined,
        caption: hasMedia && hasText ? textContent.trim() : undefined
      }

      if (hasMedia && hasText) {
        input.text = undefined // Caption is used instead of text for media messages
      }

      const result = await client.graphql({
        query: SEND_MESSAGE,
        variables: { input }
      })

      const response = (result as any).data?.sendMessage as SendMessageResponse

      if (response?.success) {
        setTextContent("")

        // Clean up media uploads
        mediaUploads.forEach((upload) => {
          if (upload.previewUrl) {
            URL.revokeObjectURL(upload.previewUrl)
          }
        })
        setMediaUploads([])

        if (textareaRef.current) {
          textareaRef.current.style.height = "40px"
        }
        onMessageSent?.(response.message)

        toast({
          title: "✅ Message Sent",
          description: "Your message has been delivered"
        })
      } else {
        toast({
          title: "Failed to send message",
          description: response?.error?.message || "Failed to send message",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error?.message || "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setSending(false)
    }
  }, [
    textContent,
    user?.organizationId,
    conversationId,
    client,
    onMessageSent,
    toast,
    sending,
    mediaUploads
  ])

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        handleSend()
      }
    },
    [handleSend]
  )

  const handleVoiceRecord = useCallback(() => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false)
      toast({
        title: "Voice recording stopped",
        description: "Voice message functionality coming soon"
      })
    } else {
      // Start recording
      setIsRecording(true)
      toast({
        title: "Voice recording started",
        description: "Voice message functionality coming soon"
      })
    }
  }, [isRecording, toast])

  const hasReadyMedia = mediaUploads.some(
    (upload) =>
      upload.s3Key &&
      (upload.status === "READY" || upload.status === "PROCESSING")
  )

  const canSend =
    !sending &&
    !disabled &&
    user?.organizationId &&
    (textContent.trim().length > 0 || hasReadyMedia)

  const showMicButton =
    !textContent.trim() && !sending && mediaUploads.length === 0

  // Close media menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isMediaMenuOpen) {
        setIsMediaMenuOpen(false)
      }
    }

    if (isMediaMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isMediaMenuOpen])

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const getStatusDisplay = (upload: MediaUpload) => {
    console.log(`🎨 Rendering status display for: ${upload.status}`)
    switch (upload.status) {
      case "UPLOADING":
        return (
          <div className="flex items-center gap-2 text-[#00a884]">
            <span>Uploading... {upload.progress || 0}%</span>
          </div>
        )
      case "PROCESSING":
        return (
          <div className="flex items-center gap-2 text-[#f59e0b]">
            <span>Processing for WhatsApp...</span>
          </div>
        )
      case "READY":
        return (
          <div className="flex items-center gap-2 text-[#00a884]">
            <span>Ready to send!</span>
          </div>
        )
      default:
        return null
    }
  }

  const handleImagePreviewClick = useCallback((url: string) => {
    // Allow preview at any time once thumbnail is available
    setPreviewImageUrl(url)
    setIsImagePreviewOpen(true)
  }, [])

  const handleCloseImagePreview = useCallback(() => {
    setIsImagePreviewOpen(false)
    setPreviewImageUrl(null)
  }, [])

  return (
    <div
      {...getRootProps()}
      className={cn(
        "bg-[#202c33] border-t border-[#2a3942] relative",
        showDragOverlay && "ring-2 ring-[#00a884] ring-opacity-50",
        className
      )}
    >
      {/* Hidden dropzone input */}
      <input {...getInputProps()} />

      {/* Drag overlay */}
      {/* {showDragOverlay && (
        <div className="absolute inset-0 bg-[#00a884] bg-opacity-20 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-[#202c33] rounded-lg p-8 border-2 border-dashed border-[#00a884] text-center">
            <Upload className="h-12 w-12 text-[#00a884] mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-[#e9edef] mb-2">
              Drop files here
            </h3>
            <p className="text-[#8696a0] text-sm">
              Images, documents, and other files up to 16MB
            </p>
          </div>
        </div>
      )} */}

      <div>
        {/* Enhanced Media Preview - WhatsApp Style */}
        {mediaUploads.length > 0 && (
          <div className="px-4 py-3 bg-[#2a3942] border-b border-[#3b4a54]">
            <div className="space-y-3">
              {mediaUploads.map((upload) => (
                <div key={upload.id} className="flex items-start gap-3">
                  {/* Media thumbnail */}
                  <div className="relative flex-shrink-0">
                    <div className="w-16 h-16 bg-[#3b4a54] rounded-lg overflow-hidden border-2 border-[#4a5c6a]">
                      {/* Show skeleton during upload */}
                      {upload.status === "UPLOADING" ? (
                        <div className="w-full h-full bg-[#4a5c6a] animate-pulse flex items-center justify-center">
                          <div className="w-8 h-8 bg-[#5a6c7a] rounded animate-pulse" />
                        </div>
                      ) : upload.previewUrl &&
                        (upload.status === "READY" ||
                          upload.status === "PROCESSING") ? (
                        <div className="relative w-full h-full cursor-pointer group">
                          {upload.mediaType.startsWith("image/") ? (
                            <LazyImage
                              src={upload.previewUrl}
                              alt={upload.fileName}
                              className="w-full h-full group-hover:opacity-75 transition-opacity"
                              onClick={() =>
                                handleImagePreviewClick(upload.previewUrl!)
                              }
                              fallbackIcon={
                                <FileImage className="h-6 w-6 text-[#8696a0]" />
                              }
                            />
                          ) : (
                            <div
                              className="w-full h-full flex items-center justify-center bg-[#4a5c6a] cursor-pointer group-hover:bg-[#5a6c7a] transition-colors"
                              onClick={() =>
                                handleImagePreviewClick(upload.previewUrl!)
                              }
                            >
                              <File className="h-6 w-6 text-[#8696a0]" />
                            </div>
                          )}
                          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                            <ZoomIn className="h-5 w-5 text-white drop-shadow-lg" />
                          </div>
                        </div>
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          {upload.mediaType.startsWith("image/") ? (
                            <FileImage className="h-6 w-6 text-[#8696a0]" />
                          ) : (
                            <File className="h-6 w-6 text-[#8696a0]" />
                          )}
                        </div>
                      )}
                    </div>

                    {/* Status overlay with improved animations */}
                    {/* {upload.status === "UPLOADING" && (
                    <div className="absolute inset-0 bg-black bg-opacity-60 rounded-lg flex flex-col items-center justify-center backdrop-blur-sm">
                      <Upload className="h-5 w-5 text-white animate-bounce mb-1" />
                      {upload.progress && (
                        <div className="w-10 bg-gray-700 rounded-full h-1">
                          <div
                            className="bg-[#00a884] h-1 rounded-full transition-all duration-300"
                            style={{ width: `${upload.progress}%` }}
                          />
                        </div>
                      )}
                    </div>
                  )} */}
                    {/* {upload.status === "PROCESSING" && (
                    <div className="absolute inset-0 bg-black bg-opacity-60 rounded-lg flex flex-col items-center justify-center backdrop-blur-sm">
                      <Loader2 className="h-5 w-5 text-white animate-spin mb-1" />
                      <div className="text-xs text-white opacity-80">Processing</div>
                    </div>
                  )} */}
                    {/* {upload.status === "READY" && (
                    <div className="absolute -top-1 -right-1 bg-[#00a884] rounded-full p-1 animate-in zoom-in duration-300">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                  )} */}
                    {upload.status === "FAILED" && (
                      <div className="absolute -top-1 -right-1 bg-red-500 rounded-full p-1 animate-in zoom-in duration-300">
                        <X className="h-4 w-4 text-white" />
                      </div>
                    )}
                  </div>

                  {/* Media info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="font-medium text-[#e9edef] truncate">
                          {upload.fileName}
                        </p>
                        <p className="text-sm text-[#8696a0]">
                          {formatFileSize(upload.fileSize)}
                        </p>

                        {upload.status === "UPLOADING" && (
                          <div className="mt-2">
                            <div className="relative w-40 h-40 rounded-xl overflow-hidden border border-[#3b4a54] bg-[#0b141a]">
                              {/* Image preview or skeleton */}
                              {upload.previewUrl ? (
                                <img
                                  src={upload.previewUrl}
                                  alt="Uploading preview"
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full animate-shimmer" />
                              )}

                              {/* Wave animation overlay */}
                              <div className="pointer-events-none absolute inset-0 animate-wave mix-blend-screen" />

                              {/* Progress dark mask from top -> bottom (reveals as it uploads) */}
                              <div
                                className="absolute inset-0 bg-black/40 transition-all duration-300 ease-out"
                                style={{
                                  clipPath: `inset(${
                                    100 - (upload.progress ?? 0)
                                  }% 0 0 0)`
                                }}
                              />

                              {/* Footer label + percent */}
                              <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-[#0b141a]/80 to-transparent flex items-center justify-between text-xs">
                                <span className="text-[#8696a0]">
                                  Uploading…
                                </span>
                                <span className="text-[#00a884]">
                                  {upload.progress ?? 0}%
                                </span>
                              </div>
                            </div>

                            {/* Optional tiny hint under the tile */}
                            <div className="mt-1 text-[10px] text-[#8696a0]">
                              Please keep this tab open.
                            </div>

                            {/* Styles (scoped if using Next.js; otherwise move to a CSS file) */}
                            <style jsx>{`
                              @keyframes shimmer {
                                0% {
                                  background-position: 200% 0;
                                }
                                100% {
                                  background-position: -200% 0;
                                }
                              }
                              .animate-shimmer {
                                background: linear-gradient(
                                  90deg,
                                  #1f2c33 20%,
                                  #26343b 35%,
                                  #1f2c33 50%
                                );
                                background-size: 200% 100%;
                                animation: shimmer 1.4s ease-in-out infinite;
                              }

                              @keyframes wave {
                                0% {
                                  background-position: 0 0;
                                }
                                100% {
                                  background-position: 200% 0;
                                }
                              }
                              .animate-wave {
                                /* diagonal soft stripes in the brand color */
                                background-image: linear-gradient(
                                  120deg,
                                  rgba(0, 168, 132, 0.18) 25%,
                                  rgba(0, 0, 0, 0) 25%,
                                  rgba(0, 0, 0, 0) 50%,
                                  rgba(0, 168, 132, 0.18) 50%,
                                  rgba(0, 168, 132, 0.18) 75%,
                                  rgba(0, 0, 0, 0) 75%,
                                  rgba(0, 0, 0, 0) 100%
                                );
                                background-size: 200% 100%;
                                animation: wave 2s linear infinite;
                              }
                            `}</style>
                          </div>
                        )}

                        <div className="mt-1">{getStatusDisplay(upload)}</div>
                      </div>

                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveMedia(upload.id)}
                        disabled={upload.status === "UPLOADING"}
                        className="p-1 h-8 w-8 text-[#8696a0] hover:text-red-400 hover:bg-[#3b4a54]"
                        title="Remove file"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>

                    {/* Progress bar for uploading only */}
                    {upload.status === "UPLOADING" &&
                      upload.progress !== undefined && (
                        <div className="mt-2 w-full bg-[#3b4a54] rounded-full h-2">
                          <div
                            className="bg-[#00a884] h-2 rounded-full transition-all duration-300"
                            style={{ width: `${upload.progress}%` }}
                          />
                        </div>
                      )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* WhatsApp Style Input Area */}
        <div className="relative">
          {/* Media Menu */}
          {isMediaMenuOpen && (
            <div className="absolute bottom-full left-4 mb-2 bg-[#414d54] rounded-lg shadow-lg border border-[#3b4a54] p-1 z-50">
              <div className="grid grid-cols-1 w-38">
                {/* Photos & Videos */}
                {/* Document */}
                <button
                  onClick={() => handleMediaMenuItemClick("document")}
                  className="flex gap-1 items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
                >
                  <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                    <FolderClosed className="h-6 w-6 text-[#16c8d4]" />
                  </div>
                  <span className="text-md text-[#e9edef] text-center">
                    File
                  </span>
                </button>
                <button
                  onClick={() => handleMediaMenuItemClick("image")}
                  className="flex gap-1  items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
                >
                  <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                    <Images className="h-6 w-6 text-[#1672d4]" />
                  </div>
                  <span className="text-md text-[#e9edef] text-center">
                    Photos & Videos
                  </span>
                </button>

                {/* Contact */}
                <button
                  onClick={() => handleMediaMenuItemClick("contact")}
                  className="flex gap-1 items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
                >
                  <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                    <CircleUserRound className="h-6 w-6 text-[#c85f09]" />
                  </div>
                  <span className="text-md text-[#e9edef] text-center">
                    Contact
                  </span>
                </button>

                {/* Contact */}
                <button
                  onClick={() => handleMediaMenuItemClick("contact")}
                  className="flex gap-2 items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
                >
                  <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                    <CalendarDays className="h-6 w-6 text-[#ad1010]" />
                  </div>
                  <span className="text-md text-[#e9edef] text-center">
                    Event
                  </span>
                </button>
              </div>
            </div>
          )}

          <div className="flex items-end gap-2 px-4 py-3">
            {/* Attachment Button - WhatsApp Style */}
            <Button
              variant="ghost"
              size="sm"
              className="h-10 w-10 p-0 hover:bg-[#3b4a54] rounded-full flex-shrink-0 transition-all text-[#8696a0] hover:text-[#e9edef]"
              disabled={
                sending ||
                mediaUploads.some((upload) => upload.status === "UPLOADING")
              }
              onClick={handleAttachClick}
              title="Attach media"
            >
              <Plus
                className={cn(
                  "h-6 w-6 transition-transform duration-200",
                  isMediaMenuOpen && "rotate-45"
                )}
              />
            </Button>

            {/* Text Input Container - WhatsApp Style */}
            <div className="flex-1 relative bg-[#2a3942] rounded-[21px] border border-[#3b4a54] min-h-[42px] flex items-center">
              <textarea
                ref={textareaRef}
                value={textContent}
                onChange={handleTextChange}
                onKeyDown={handleKeyPress}
                placeholder={
                  mediaUploads.some((upload) => upload.status === "READY")
                    ? "Add a caption..."
                    : mediaUploads.some(
                        (upload) => upload.status === "PROCESSING"
                      )
                    ? "Processing files..."
                    : mediaUploads.some(
                        (upload) => upload.status === "UPLOADING"
                      )
                    ? "Uploading files..."
                    : "Type a message"
                }
                disabled={
                  sending ||
                  mediaUploads.some((upload) => upload.status === "UPLOADING")
                }
                className="w-full bg-transparent px-4 py-2.5 pr-12 text-[15px] resize-none focus:outline-none placeholder:text-[#8696a0] disabled:opacity-50 leading-5 text-[#e9edef]"
                style={{
                  height: "42px",
                  minHeight: "42px",
                  maxHeight: "120px"
                }}
                rows={1}
              />

              {/* Emoji Button - WhatsApp Style */}
              <Popover
                open={isEmojiPickerOpen}
                onOpenChange={setIsEmojiPickerOpen}
              >
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="absolute right-3 top-1/2 -translate-y-1/2 h-6 w-10 p-0 hover:bg-[#3b4a54] text-[#8696a0] hover:text-[#e9edef] rounded-full flex-shrink-0"
                    disabled={
                      sending ||
                      mediaUploads.some(
                        (upload) => upload.status === "UPLOADING"
                      )
                    }
                    title="Insert emoji"
                    onClick={handleEmojiButtonClick}
                  >
                    <Smile className="h-5 w-5" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  className="w-80 p-0 border-[#3b4a54] bg-[#2a3942]"
                  side="top"
                  align="end"
                  sideOffset={8}
                >
                  <EmojiPicker
                    onEmojiClick={handleEmojiClick}
                    theme={Theme.DARK}
                    searchDisabled={false}
                    skinTonesDisabled={false}
                    width="100%"
                    height={350}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Microphone or Send Button - WhatsApp Style */}
            {showMicButton ? (
              <Button
                onClick={handleVoiceRecord}
                size="sm"
                className={cn(
                  "h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all",
                  isRecording
                    ? "bg-red-600 hover:bg-red-700 text-white"
                    : "bg-[#00a884] hover:bg-[#00a884]/90 text-white"
                )}
                title="Record voice message"
              >
                <Mic
                  className={cn("h-5 w-5", isRecording && "animate-pulse")}
                />
              </Button>
            ) : (
              <Button
                onClick={handleSend}
                disabled={!canSend}
                size="sm"
                className={cn(
                  "h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all",
                  canSend
                    ? "bg-[#00a884] hover:bg-[#00a884]/90 text-white"
                    : "bg-[#3b4a54] text-[#8696a0] cursor-not-allowed"
                )}
                title="Send message"
              >
                {sending ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <Send className="h-5 w-5" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Image Preview Modal */}
        <div className="[&_.fixed]:!z-[9999]">
          <Dialog
            open={isImagePreviewOpen}
            onOpenChange={handleCloseImagePreview}
          >
            <DialogContent className="max-w-4xl max-h-[90vh] p-0">
              <DialogHeader className="p-6 pb-0">
                <DialogTitle className="text-lg font-semibold">
                  File Preview
                </DialogTitle>
              </DialogHeader>
              <div className="p-6 flex items-center justify-center min-h-[400px]">
                {previewImageUrl ? (
                  <LazyImage
                    src={previewImageUrl}
                    alt="Preview"
                    className="max-w-full max-h-[70vh] object-contain rounded-lg"
                    fallbackIcon={
                      <div className="flex flex-col items-center gap-2 text-gray-400">
                        <FileImage className="h-12 w-12" />
                        <span className="text-sm">Failed to load image</span>
                      </div>
                    }
                  />
                ) : (
                  <div className="flex items-center justify-center w-full h-64 bg-gray-100 rounded-lg">
                    <div className="text-center text-gray-400">
                      <FileImage className="h-12 w-12 mx-auto mb-2" />
                      <span className="text-sm">No image to preview</span>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>
    </div>
  )
}
