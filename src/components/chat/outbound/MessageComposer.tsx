import { useState, use<PERSON><PERSON>back, useRef, useEffect } from "react"
import { generateClient } from "aws-amplify/api"
import { executeSubscription } from "@/lib/graphql-client"
import { ON_MEDIA_UPLOAD_STATUS_CHANGED } from "@/lib/graphql-operations"
import { useAuth } from "@/hooks/useAuth"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { uploadFileToS3 } from "@/lib/s3/upload"
import {
  SEND_MESSAGE,
  SendMessageInput,
  SendMessageResponse
} from "@/lib/graphql/mutations/sendMessage"
import {
  Plus,
  Send,
  Smile,
  Mic,
  Loader2,
  CheckCircle,
  FolderClosed,
  CircleUserRound,
  X,
  FileImage,
  Images,
  Upload,
  ZoomIn,
  CalendarDays,
  File
} from "lucide-react"

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog"
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger
} from "@/components/ui/popover"
import EmojiPicker, { EmojiClickData, Theme } from "emoji-picker-react"
import { cn } from "@/lib/utils"

interface MessageComposerProps {
  conversationId: string
  onMessageSent?: (message: any) => void
  disabled?: boolean
  className?: string
}

// Enhanced media upload status tracking
interface MediaUpload {
  id: string // Unique identifier for each file
  s3Key: string
  fileName: string
  mediaId?: string
  status: "UPLOADING" | "PROCESSING" | "READY" | "FAILED"
  fileSize: number
  mediaType: string // Store the actual file MIME type
  progress?: number
  previewUrl?: string
  file?: File // Store original file for preview
}

export const MessageComposer: React.FC<MessageComposerProps> = ({
  conversationId,
  onMessageSent,
  disabled = false,
  className = ""
}) => {
  const { user } = useAuth()
  const { toast } = useToast()
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // State
  const [textContent, setTextContent] = useState("")
  const [sending, setSending] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [mediaUploads, setMediaUploads] = useState<MediaUpload[]>([])
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false)
  const [previewImageUrl, setPreviewImageUrl] = useState<string | null>(null)
  const [isMediaMenuOpen, setIsMediaMenuOpen] = useState(false)
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false)

  // Initialize Amplify GraphQL client
  const client = generateClient()

  // Subscribe to media upload status updates
  useEffect(() => {
    if (mediaUploads.length === 0 || mediaUploads.every(upload => upload.status === "UPLOADING")) return

    console.log(
      "🎬 Setting up media upload subscription for conversation:",
      conversationId
    )

    const subscription = executeSubscription(
      {
        query: ON_MEDIA_UPLOAD_STATUS_CHANGED,
        variables: { conversationId }
      },
      {
        next: (data: any) => {
          console.log("🎬 Media upload status update:", data)
          const update = data.onMediaUploadStatusChanged

          // Find matching upload by filename
          const backendFilename = update?.s3Key.split("/").pop()
          const matchingUpload = mediaUploads.find(upload => {
            const frontendFilename = upload.s3Key.split("/").pop()
            return frontendFilename === backendFilename
          })

          if (update && matchingUpload) {
            console.log(
              `🔍 Updating upload ${matchingUpload.id} status to: ${update.status}`
            )

            setMediaUploads(prev =>
              prev.map(upload =>
                upload.id === matchingUpload.id
                  ? {
                      ...upload,
                      status: update.status, // Keep backend status format (uppercase)
                      mediaId: update.mediaId || upload.mediaId,
                      fileName: update.fileName || upload.fileName,
                      fileSize: update.fileSize !== undefined ? update.fileSize : upload.fileSize
                    }
                  : upload
              )
            )

            // Show success toast when ready
            if (update.status === "READY") {
              console.log("🎉 File is READY! Showing toast...")
              const isImage = matchingUpload.mediaType.startsWith("image/")
              toast({
                title: `✅ ${isImage ? "Image" : "File"} Ready`,
                description: `${matchingUpload.fileName} is ready to send!`
              })
            }
          }
        },
        error: (error: any) => {
          console.error("Media upload subscription error:", error)
          toast({
            title: "Connection Error",
            description: "Lost connection to upload updates",
            variant: "destructive"
          })
        }
      }
    )

    return () => {
      subscription?.unsubscribe()
    }
  }, [mediaUploads, conversationId, toast])

  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current
    if (textarea) {
      textarea.style.height = "auto"
      const newHeight = Math.min(Math.max(textarea.scrollHeight, 40), 120)
      textarea.style.height = newHeight + "px"
    }
  }, [])

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(event.target.files || [])
    if (files.length === 0 || !user?.organizationId) return

    // Process each file
    for (const file of files) {
      // Validate file type and size
      const isImage = file.type.startsWith("image/")
      const isDocument = file.type.startsWith("application/") || file.type.startsWith("text/")

      if (!isImage && !isDocument) {
        toast({
          title: "Invalid File Type",
          description: `${file.name}: Please select an image or document file`,
          variant: "destructive"
        })
        continue
      }

      if (file.size > 16 * 1024 * 1024) {
        // 16MB limit
        toast({
          title: "File Too Large",
          description: `${file.name}: Please select a file smaller than 16MB`,
          variant: "destructive"
        })
        continue
      }

      await uploadSingleFile(file)
    }
  }

  const uploadSingleFile = async (file: File) => {
    if (!user?.organizationId) return

    // Extract phoneNumberId from conversationId (format: orgId#phoneNumberId#customerPhone)
    const conversationParts = conversationId.split("#")
    const phoneNumberId =
      conversationParts.length >= 2 ? conversationParts[1] : ""

    if (!phoneNumberId) {
      toast({
        title: "Upload Failed",
        description: "Could not determine phone number ID from conversation.",
        variant: "destructive"
      })
      return
    }

    // Create preview URL for internal use
    const previewUrl = URL.createObjectURL(file)
    const fileId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

    // Create new media upload object
    const newMediaUpload: MediaUpload = {
      id: fileId,
      s3Key: "", // Will be set after upload
      fileName: file.name,
      mediaType: file.type, // Store actual MIME type (e.g., 'image/png', 'image/jpeg')
      status: "UPLOADING",
      fileSize: file.size,
      progress: 0,
      previewUrl, // Store it but UI will only show when status is READY
      file
    }

    // Add to uploads array
    setMediaUploads(prev => [...prev, newMediaUpload])

    const isImage = file.type.startsWith("image/")
    toast({
      title: `📤 Uploading ${isImage ? "Image" : "File"}`,
      description: `Preparing ${file.name} for upload...`
    })

    const uploadResult = await uploadFileToS3(
      file,
      user.organizationId,
      conversationId,
      phoneNumberId,
      (progress) => {
        console.log(`Upload progress:`, progress)
        if (
          progress &&
          progress.loaded !== undefined &&
          progress.total !== undefined
        ) {
          const percentage = Math.round(
            (progress.loaded / progress.total) * 100
          )
          setMediaUploads((prev) =>
            prev.map(upload =>
              upload.id === fileId
                ? { ...upload, progress: percentage }
                : upload
            )
          )
        }
      }
    )

    if (uploadResult.success && uploadResult.key) {
      // Update to processing state
      setMediaUploads((prev) =>
        prev.map(upload =>
          upload.id === fileId
            ? {
                ...upload,
                s3Key: uploadResult.key!,
                status: "PROCESSING",
                progress: 100
              }
            : upload
        )
      )

      const isImage = file.type.startsWith("image/")
      toast({
        title: `🔄 Processing ${isImage ? "Image" : "File"}`,
        description: `Optimizing ${file.name} for WhatsApp...`
      })
    } else {
      // Clean up preview URL and remove from uploads
      URL.revokeObjectURL(previewUrl)
      setMediaUploads((prev) => prev.filter(upload => upload.id !== fileId))

      toast({
        title: "Upload Failed",
        description: `Could not upload ${file.name}. Please try again.`,
        variant: "destructive"
      })
    }

    // Reset file input to allow re-uploading the same file
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  const handleRemoveMedia = useCallback((fileId: string) => {
    const uploadToRemove = mediaUploads.find(upload => upload.id === fileId)
    if (uploadToRemove?.previewUrl) {
      URL.revokeObjectURL(uploadToRemove.previewUrl)
    }
    setMediaUploads(prev => prev.filter(upload => upload.id !== fileId))
    toast({
      title: "Media Removed",
      description: "File removed from message"
    })
  }, [mediaUploads, toast])

  const handleAttachClick = () => {
    setIsMediaMenuOpen(!isMediaMenuOpen)
  }

  const handleMediaMenuItemClick = (
    type: "image" | "camera" | "document" | "location" | "contact"
  ) => {
    console.log("🎯 handleMediaMenuItemClick called with type:", type)
    console.log("📁 fileInputRef.current:", fileInputRef.current)

    setIsMediaMenuOpen(false)

    switch (type) {
      case "image":
        console.log("🖼️ Image case - attempting to open file picker")
        // Set accept attribute for images only
        if (fileInputRef.current) {
          console.log("✅ fileInputRef.current exists, setting accept and clicking")
          fileInputRef.current.accept = "image/*"

          // Try to trigger click with a small delay to ensure DOM is ready
          setTimeout(() => {
            if (fileInputRef.current) {
              fileInputRef.current.click()
              console.log("🔄 Click triggered")
            }
          }, 10)
        } else {
          console.error("❌ fileInputRef.current is null - creating temporary input")
          // Fallback: create temporary file input
          const tempInput = document.createElement('input')
          tempInput.type = 'file'
          tempInput.accept = 'image/*'
          tempInput.multiple = true
          tempInput.style.display = 'none'
          document.body.appendChild(tempInput)

          tempInput.onchange = (e) => {
            const event = e as unknown as React.ChangeEvent<HTMLInputElement>
            handleFileChange(event)
            document.body.removeChild(tempInput)
          }

          tempInput.click()
        }
        break
      case "camera":
        // Camera functionality - for now just open file picker for images
        if (fileInputRef.current) {
          fileInputRef.current.accept = "image/*"
          fileInputRef.current.click()
        }
        break
      case "document":
        // Open file picker for documents
        if (fileInputRef.current) {
          fileInputRef.current.accept = "application/*,text/*,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx"
          fileInputRef.current.click()
        }
        break
      case "location":
        toast({
          title: "Coming Soon",
          description: "Location sharing will be available soon"
        })
        break
      case "contact":
        toast({
          title: "Coming Soon",
          description: "Contact sharing will be available soon"
        })
        break
    }
  }

  const handleTextChange = useCallback(
    (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      setTextContent(e.target.value)
      adjustTextareaHeight()
    },
    [adjustTextareaHeight]
  )

  // Emoji picker handlers
  const handleEmojiClick = useCallback((emojiData: EmojiClickData) => {
    const emoji = emojiData.emoji
    const textarea = textareaRef.current
    if (textarea) {
      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const newText = textContent.slice(0, start) + emoji + textContent.slice(end)
      setTextContent(newText)

      // Set cursor position after emoji
      setTimeout(() => {
        textarea.focus()
        textarea.setSelectionRange(start + emoji.length, start + emoji.length)
      }, 0)
    } else {
      setTextContent(prev => prev + emoji)
    }
    setIsEmojiPickerOpen(false)
  }, [textContent])

  const handleEmojiButtonClick = useCallback(() => {
    setIsEmojiPickerOpen(!isEmojiPickerOpen)
  }, [isEmojiPickerOpen])

  const handleSend = useCallback(async () => {
    const hasText = textContent.trim().length > 0
    const readyMedia = mediaUploads.find(upload =>
      upload.s3Key && (upload.status === "READY" || upload.status === "PROCESSING")
    )
    const hasMedia = !!readyMedia

    if ((!hasText && !hasMedia) || !user?.organizationId || sending) return

    setSending(true)

    try {
      const input: SendMessageInput = {
        conversationId,
        organizationId: user.organizationId,
        messageType: hasMedia ? (readyMedia.mediaType.startsWith("image/") ? "IMAGE" : "DOCUMENT") : "TEXT",
        text: hasText ? textContent.trim() : undefined,
        mediaUrl: hasMedia ? readyMedia.s3Key : undefined, // Use S3 key instead of mediaId
        mediaType: hasMedia ? readyMedia.mediaType : undefined, // Use actual file MIME type
        fileName: hasMedia ? readyMedia.fileName : undefined,
        fileSize: hasMedia ? readyMedia.fileSize : undefined,
        caption: hasMedia && hasText ? textContent.trim() : undefined
      }

      if (hasMedia && hasText) {
        input.text = undefined // Caption is used instead of text for media messages
      }

      const result = await client.graphql({
        query: SEND_MESSAGE,
        variables: { input }
      })

      const response = (result as any).data?.sendMessage as SendMessageResponse

      if (response?.success) {
        setTextContent("")

        // Clean up media uploads
        mediaUploads.forEach(upload => {
          if (upload.previewUrl) {
            URL.revokeObjectURL(upload.previewUrl)
          }
        })
        setMediaUploads([])

        if (textareaRef.current) {
          textareaRef.current.style.height = "40px"
        }
        onMessageSent?.(response.message)

        toast({
          title: "✅ Message Sent",
          description: "Your message has been delivered"
        })
      } else {
        toast({
          title: "Failed to send message",
          description: response?.error?.message || "Failed to send message",
          variant: "destructive"
        })
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error?.message || "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setSending(false)
    }
  }, [
    textContent,
    user?.organizationId,
    conversationId,
    client,
    onMessageSent,
    toast,
    sending,
    mediaUploads
  ])

  const handleKeyPress = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault()
        handleSend()
      }
    },
    [handleSend]
  )

  const handleVoiceRecord = useCallback(() => {
    if (isRecording) {
      // Stop recording
      setIsRecording(false)
      toast({
        title: "Voice recording stopped",
        description: "Voice message functionality coming soon"
      })
    } else {
      // Start recording
      setIsRecording(true)
      toast({
        title: "Voice recording started",
        description: "Voice message functionality coming soon"
      })
    }
  }, [isRecording, toast])

  const hasReadyMedia = mediaUploads.some(upload =>
    upload.s3Key && (upload.status === "READY" || upload.status === "PROCESSING")
  )

  const canSend =
    !sending &&
    !disabled &&
    user?.organizationId &&
    (textContent.trim().length > 0 || hasReadyMedia)

  const showMicButton = !textContent.trim() && !sending && mediaUploads.length === 0

  // Close media menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isMediaMenuOpen) {
        setIsMediaMenuOpen(false)
      }
    }

    if (isMediaMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isMediaMenuOpen])

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"
    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  const getStatusDisplay = (upload: MediaUpload) => {
    console.log(`🎨 Rendering status display for: ${upload.status}`)
    switch (upload.status) {
      case "UPLOADING":
        return (
          <div className="flex items-center gap-2 text-[#00a884]">
            <span>Uploading... {upload.progress || 0}%</span>
          </div>
        )
      case "PROCESSING":
        return (
          <div className="flex items-center gap-2 text-[#f59e0b]">
            <span>Processing for WhatsApp...</span>
          </div>
        )
      case "READY":
        return (
          <div className="flex items-center gap-2 text-[#00a884]">
            <span>Ready to send!</span>
          </div>
        )
      default:
        return null
    }
  }

  const handleImagePreviewClick = useCallback((url: string) => {
    // Allow preview at any time once thumbnail is available
    setPreviewImageUrl(url)
    setIsImagePreviewOpen(true)
  }, [])

  const handleCloseImagePreview = useCallback(() => {
    setIsImagePreviewOpen(false)
    setPreviewImageUrl(null)
  }, [])

  return (
    <div className={cn("bg-[#202c33] border-t border-[#2a3942]", className)}>
      {/* Enhanced Media Preview - WhatsApp Style */}
      {mediaUploads.length > 0 && (
        <div className="px-4 py-3 bg-[#2a3942] border-b border-[#3b4a54]">
          <div className="space-y-3">
            {mediaUploads.map((upload) => (
              <div key={upload.id} className="flex items-start gap-3">
                {/* Media thumbnail */}
                <div className="relative flex-shrink-0">
                  <div className="w-16 h-16 bg-[#3b4a54] rounded-lg overflow-hidden border-2 border-[#4a5c6a]">
                    {upload.previewUrl &&
                    (upload.status === "READY" ||
                      upload.status === "PROCESSING") ? (
                      <div
                        className="relative w-full h-full cursor-pointer group"
                        onClick={() =>
                          handleImagePreviewClick(upload.previewUrl!)
                        }
                      >
                        {upload.mediaType.startsWith("image/") ? (
                          <img
                            src={upload.previewUrl}
                            alt={upload.fileName}
                            className="w-full h-full object-cover transition-opacity group-hover:opacity-75"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-[#4a5c6a]">
                            <File className="h-6 w-6 text-[#8696a0]" />
                          </div>
                        )}
                        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
                          <ZoomIn className="h-5 w-5 text-white drop-shadow-lg" />
                        </div>
                      </div>
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        {upload.mediaType.startsWith("image/") ? (
                          <FileImage className="h-6 w-6 text-[#8696a0]" />
                        ) : (
                          <File className="h-6 w-6 text-[#8696a0]" />
                        )}
                      </div>
                    )}
                  </div>

                  {/* Status overlay - only show for uploading/processing */}
                  {upload.status === "UPLOADING" && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                      <Upload className="h-6 w-6 text-white animate-bounce" />
                    </div>
                  )}
                  {upload.status === "PROCESSING" && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
                      <Loader2 className="h-6 w-6 text-white animate-spin" />
                    </div>
                  )}
                  {upload.status === "READY" && (
                    <div className="absolute -top-1 -right-1 bg-[#00a884] rounded-full p-1">
                      <CheckCircle className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>

                {/* Media info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-medium text-[#e9edef] truncate">
                        {upload.fileName}
                      </p>
                      <p className="text-sm text-[#8696a0]">
                        {formatFileSize(upload.fileSize)}
                      </p>
                      <div className="mt-1">{getStatusDisplay(upload)}</div>
                    </div>

                    {/* Remove button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveMedia(upload.id)}
                      disabled={upload.status === "UPLOADING"}
                      className="p-1 h-8 w-8 text-[#8696a0] hover:text-red-400 hover:bg-[#3b4a54]"
                      title="Remove file"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>

                  {/* Progress bar for uploading only */}
                  {upload.status === "UPLOADING" &&
                    upload.progress !== undefined && (
                      <div className="mt-2 w-full bg-[#3b4a54] rounded-full h-2">
                        <div
                          className="bg-[#00a884] h-2 rounded-full transition-all duration-300"
                          style={{ width: `${upload.progress}%` }}
                        />
                      </div>
                    )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* WhatsApp Style Input Area */}
      <div className="relative">
        {/* Media Menu */}
        {isMediaMenuOpen && (
          <div className="absolute bottom-full left-4 mb-2 bg-[#414d54] rounded-lg shadow-lg border border-[#3b4a54] p-1 z-50">
            <div className="grid grid-cols-1 w-38">
              {/* Photos & Videos */}
              {/* Document */}
              <button
                onClick={() => handleMediaMenuItemClick("document")}
                className="flex gap-1 items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
              >
                <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                  <FolderClosed className="h-6 w-6 text-[#16c8d4]" />
                </div>
                <span className="text-md text-[#e9edef] text-center">File</span>
              </button>
              <button
                onClick={() => handleMediaMenuItemClick("image")}
                className="flex gap-1  items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
              >
                <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                  <Images className="h-6 w-6 text-[#1672d4]" />
                </div>
                <span className="text-md text-[#e9edef] text-center">
                  Photos & Videos
                </span>
              </button>

              {/* Contact */}
              <button
                onClick={() => handleMediaMenuItemClick("contact")}
                className="flex gap-1 items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
              >
                <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                  <CircleUserRound className="h-6 w-6 text-[#c85f09]" />
                </div>
                <span className="text-md text-[#e9edef] text-center">
                  Contact
                </span>
              </button>

              {/* Contact */}
              <button
                onClick={() => handleMediaMenuItemClick("contact")}
                className="flex gap-2 items-center p-3 rounded-lg hover:bg-[#3b4a54] transition-colors group"
              >
                <div className="w-10 h-6  rounded-full flex items-center justify-center  group-hover:scale-110 transition-transform">
                  <CalendarDays className="h-6 w-6 text-[#ad1010]" />
                </div>
                <span className="text-md text-[#e9edef] text-center">
                  Event
                </span>
              </button>
            </div>
          </div>
        )}

        <div className="flex items-end gap-2 px-4 py-3">
          {/* Attachment Input */}
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            style={{ position: 'absolute', left: '-9999px', opacity: 0 }}
            accept="image/*,application/*,text/*"
            multiple
          />

          {/* Attachment Button - WhatsApp Style */}
          <Button
            variant="ghost"
            size="sm"
            className="h-10 w-10 p-0 hover:bg-[#3b4a54] rounded-full flex-shrink-0 transition-all text-[#8696a0] hover:text-[#e9edef]"
            disabled={
              sending ||
              mediaUploads.some(upload => upload.status === "UPLOADING")
            }
            onClick={handleAttachClick}
            title="Attach media"
          >
            <Plus
              className={cn(
                "h-6 w-6 transition-transform duration-200",
                isMediaMenuOpen && "rotate-45"
              )}
            />
          </Button>

          {/* Text Input Container - WhatsApp Style */}
          <div className="flex-1 relative bg-[#2a3942] rounded-[21px] border border-[#3b4a54] min-h-[42px] flex items-center">
            <textarea
              ref={textareaRef}
              value={textContent}
              onChange={handleTextChange}
              onKeyDown={handleKeyPress}
              placeholder={
                mediaUploads.some(upload => upload.status === "READY")
                  ? "Add a caption..."
                  : mediaUploads.some(upload => upload.status === "PROCESSING")
                  ? "Processing files..."
                  : mediaUploads.some(upload => upload.status === "UPLOADING")
                  ? "Uploading files..."
                  : "Type a message"
              }
              disabled={sending || mediaUploads.some(upload => upload.status === "UPLOADING")}
              className="w-full bg-transparent px-4 py-2.5 pr-12 text-[15px] resize-none focus:outline-none placeholder:text-[#8696a0] disabled:opacity-50 leading-5 text-[#e9edef]"
              style={{
                height: "42px",
                minHeight: "42px",
                maxHeight: "120px"
              }}
              rows={1}
            />

            {/* Emoji Button - WhatsApp Style */}
            <Popover open={isEmojiPickerOpen} onOpenChange={setIsEmojiPickerOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-3 top-1/2 -translate-y-1/2 h-6 w-10 p-0 hover:bg-[#3b4a54] text-[#8696a0] hover:text-[#e9edef] rounded-full flex-shrink-0"
                  disabled={sending || mediaUploads.some(upload => upload.status === "UPLOADING")}
                  title="Insert emoji"
                  onClick={handleEmojiButtonClick}
                >
                  <Smile className="h-5 w-5" />
                </Button>
              </PopoverTrigger>
              <PopoverContent
                className="w-80 p-0 border-[#3b4a54] bg-[#2a3942]"
                side="top"
                align="end"
                sideOffset={8}
              >
                <EmojiPicker
                  onEmojiClick={handleEmojiClick}
                  theme={Theme.DARK}
                  searchDisabled={false}
                  skinTonesDisabled={false}
                  width="100%"
                  height={350}
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Microphone or Send Button - WhatsApp Style */}
          {showMicButton ? (
            <Button
              onClick={handleVoiceRecord}
              size="sm"
              className={cn(
                "h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all",
                isRecording
                  ? "bg-red-600 hover:bg-red-700 text-white"
                  : "bg-[#00a884] hover:bg-[#00a884]/90 text-white"
              )}
              title="Record voice message"
            >
              <Mic className={cn("h-5 w-5", isRecording && "animate-pulse")} />
            </Button>
          ) : (
            <Button
              onClick={handleSend}
              disabled={!canSend}
              size="sm"
              className={cn(
                "h-10 w-10 p-0 rounded-full flex-shrink-0 transition-all",
                canSend
                  ? "bg-[#00a884] hover:bg-[#00a884]/90 text-white"
                  : "bg-[#3b4a54] text-[#8696a0] cursor-not-allowed"
              )}
              title="Send message"
            >
              {sending ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <Send className="h-5 w-5" />
              )}
            </Button>
          )}
        </div>
      </div>

      {/* Image Preview Modal */}
      <div className="[&_.fixed]:!z-[9999]">
        <Dialog
          open={isImagePreviewOpen}
          onOpenChange={handleCloseImagePreview}
        >
          <DialogContent className="max-w-4xl max-h-[90vh] p-0">
            <DialogHeader className="p-6 pb-0">
              <DialogTitle className="text-lg font-semibold">
                File Preview
              </DialogTitle>
            </DialogHeader>
            <div className="p-6 flex items-center justify-center">
              <img
                src={previewImageUrl || ""}
                alt="Preview"
                className="max-w-full max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
